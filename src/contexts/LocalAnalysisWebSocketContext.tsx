import React, { createContext, useEffect, useState, useRef, useCallback } from 'react'
import type { ChunkResult } from '../types/analysis'

interface LocalWebSocketContextType {
  isConnected: boolean
  progress: number
  chunkResults: ChunkResult[]
  error: string | null
  connect: () => void
  disconnect: () => void
  clearResults: () => void
}

const LocalWebSocketContext = createContext<LocalWebSocketContextType>({
  isConnected: false,
  progress: 0,
  chunkResults: [],
  error: null,
  connect: () => {},
  disconnect: () => {},
  clearResults: () => {},
})

interface LocalWebSocketProviderProps {
  children: React.ReactNode
  url?: string
}

export const LocalWebSocketProvider: React.FC<LocalWebSocketProviderProps> = ({ 
  children, 
  url = import.meta.env.VITE_LOCAL_WEBSOCKET_URL || 'ws://localhost:8000/ws' 
}) => {
  const [isConnected, setIsConnected] = useState(false)
  const [progress, setProgress] = useState(0)
  const [chunkResults, setChunkResults] = useState<ChunkResult[]>([])
  const [error, setError] = useState<string | null>(null)
  const wsRef = useRef<WebSocket | null>(null)
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined)
  const reconnectAttemptsRef = useRef(0)

  const clearResults = useCallback(() => {
    setChunkResults([])
    setProgress(0)
    setError(null)
  }, [])

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      console.log('Local WebSocket already connected')
      return
    }

    console.log('Connecting to Local WebSocket:', url)
    const ws = new WebSocket(url)
    wsRef.current = ws

    ws.onopen = () => {
      console.log('Local WebSocket connected')
      setIsConnected(true)
      setError(null)
      reconnectAttemptsRef.current = 0
    }

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data)
        console.log('Local WebSocket message:', message)

        switch (message.type) {
          case 'status':
            console.log('Status:', message.message)
            break

          case 'preview':
            console.log('Preview data received:', message.data)
            break

          case 'chunk':
            if (message.data) {
              const chunkData = message.data
              setChunkResults((prev) => {
                // Check if chunk already exists
                const existingIndex = prev.findIndex(c => c.chunk_number === chunkData.chunk_number)
                if (existingIndex >= 0) {
                  const updated = [...prev]
                  updated[existingIndex] = chunkData
                  return updated
                }
                return [...prev, chunkData]
              })
              if (chunkData.progress !== undefined) {
                setProgress(chunkData.progress)
              }
            }
            break

          case 'complete':
            console.log('Analysis complete:', message.data)
            setProgress(100)
            break

          case 'validation_error':
            setError(message.message || 'Validation error')
            if (message.errors) {
              console.error('Validation errors:', message.errors)
            }
            break

          case 'validation_warning':
            if (message.warnings) {
              console.warn('Validation warnings:', message.warnings)
            }
            break

          case 'error':
            setError(message.message || 'Unknown error')
            console.error('Local WebSocket error:', message)
            break

          default:
            console.log('Unknown message type:', message.type, message)
        }
      } catch (err) {
        console.error('Error parsing Local WebSocket message:', err)
      }
    }

    ws.onerror = (error) => {
      console.error('Local WebSocket error:', error)
      setError('Connection error')
      setIsConnected(false)
    }

    ws.onclose = (event) => {
      console.log('Local WebSocket disconnected:', event.code, event.reason)
      setIsConnected(false)
      
      // Attempt reconnection if not a normal closure
      if (event.code !== 1000 && reconnectAttemptsRef.current < 5) {
        reconnectAttemptsRef.current++
        const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 10000)
        console.log(`Attempting reconnection ${reconnectAttemptsRef.current}/5 in ${delay}ms`)
        
        reconnectTimeoutRef.current = setTimeout(() => {
          connect()
        }, delay)
      }
    }
  }, [url])

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (wsRef.current) {
      if (wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.close(1000, 'User disconnect')
      }
      wsRef.current = null
    }
    
    setIsConnected(false)
    reconnectAttemptsRef.current = 0
  }, [])

  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  const value: LocalWebSocketContextType = {
    isConnected,
    progress,
    chunkResults,
    error,
    connect,
    disconnect,
    clearResults,
  }

  return (
    <LocalWebSocketContext.Provider value={value}>
      {children}
    </LocalWebSocketContext.Provider>
  )
}

export default LocalWebSocketContext