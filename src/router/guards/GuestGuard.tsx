import type { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'

interface GuestGuardProps {
  children: ReactNode
}

import { useSelector } from 'react-redux'
import type { RootState } from '../../store'

const useAuth = () => {
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth)
  return {
    isAuthenticated,
    isLoading,
  }
}

export function GuestGuard({ children }: GuestGuardProps) {
  // TEMPORARY: Authentication bypass for development/testing
  // TODO: Remove this bypass and uncomment the original authentication logic below
  const BYPASS_AUTH = true

  if (BYPASS_AUTH) {
    return <>{children}</>
  }

  // ORIGINAL AUTHENTICATION LOGIC (temporarily disabled)
  // Uncomment the code below and set BYPASS_AUTH to false to re-enable authentication
  /*
  const { isAuthenticated, isLoading } = useAuth()
  const location = useLocation()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
      </div>
    )
  }

  if (isAuthenticated) {
    // Redirect to the intended page or dashboard
    const from = (location.state as { from?: { pathname: string } })?.from?.pathname || '/'
    return <Navigate to={from} replace />
  }

  return <>{children}</>
  */
}
