import { useEffect } from 'react'
import type { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../hooks/useAuth'

interface AuthGuardProps {
  children: ReactNode
}

export function AuthGuard({ children }: AuthGuardProps) {
  // TEMPORARY: Authentication bypass for development/testing
  // TODO: Remove this bypass and uncomment the original authentication logic below
  const BYPASS_AUTH = true

  if (BYPASS_AUTH) {
    return <>{children}</>
  }

  // ORIGINAL AUTHENTICATION LOGIC (temporarily disabled)
  // Uncomment the code below and set BYPASS_AUTH to false to re-enable authentication
  /*
  const { isAuthenticated, isLoading, checkAuth } = useAuth()
  const location = useLocation()

  useEffect(() => {
    checkAuth()
  }, [checkAuth])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />
  }

  return <>{children}</>
  */
}
