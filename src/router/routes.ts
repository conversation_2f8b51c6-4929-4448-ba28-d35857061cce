export interface RouteConfig {
  path: string
  element: string
  title: string
  requireAuth?: boolean
  guestOnly?: boolean
}

// TEMPORARY: Authentication bypass - all requireAuth and guestOnly flags disabled
// TODO: Restore original authentication requirements by uncommenting the commented lines below
export const routes: RouteConfig[] = [
  {
    path: '/',
    element: 'Dashboard',
    title: 'Dashboard',
    // requireAuth: true, // TEMPORARILY DISABLED
  },
  {
    path: '/configuration',
    element: 'ConfigurationParameters',
    title: 'Configuration Parameters',
    // requireAuth: true, // TEMPORARILY DISABLED
  },
  {
    path: '/analysis/:fileId',
    element: 'Analysis',
    title: 'Analysis',
    // requireAuth: true, // TEMPORARILY DISABLED
  },
  {
    path: '/login',
    element: 'Login',
    title: 'Login',
    // guestOnly: true, // TEMPORARILY DISABLED
  },
  {
    path: '/signup',
    element: 'SignUp',
    title: 'Sign Up',
    // guestOnly: true, // TEMPORARILY DISABLED
  },
  {
    path: '/forgot-password',
    element: 'ForgotPassword',
    title: 'Forgot Password',
    // guestOnly: true, // TEMPORARILY DISABLED
  },
  {
    path: '/request-demo',
    element: 'RequestDemo',
    title: 'Request Demo',
    // guestOnly: true, // TEMPORARILY DISABLED
  },
  {
    path: '*',
    element: 'NotFound',
    title: 'Page Not Found',
  },
]
