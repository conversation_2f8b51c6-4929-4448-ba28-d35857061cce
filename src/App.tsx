import { Provider } from 'react-redux'
import { PersistGate } from 'redux-persist/integration/react'
import { Toaster } from 'sonner'
import { ErrorBoundary } from './components/ErrorBoundary/ErrorBoundary'
import { AppRouting } from './routing'
import { store, persistor } from './store'
import { WebSocketProvider } from './contexts/WebSocketContext'

function App() {
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <WebSocketProvider>
          <ErrorBoundary>
            <AppRouting />

            <Toaster
              position="bottom-right"
              richColors
              closeButton
              duration={4000}
              theme="light"
              toastOptions={{
                style: {
                  fontFamily: 'Inter, system-ui, sans-serif',
                },
              }}
            />
          </ErrorBoundary>
        </WebSocketProvider>
      </PersistGate>
    </Provider>
  )
}

export default App
