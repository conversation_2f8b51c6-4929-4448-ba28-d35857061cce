import { useEffect } from 'react'
import { Form, Input, Select, Checkbox, Button } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import { PageLayout } from '../components/PageLayout'
import { PageHeader } from '../components/PageHeader'
import { useAppDispatch, useAppSelector } from '../store'
import { setGlobalSettings, defaultAnalysisSettings } from '../store/slices/analysisSettingsSlice'
import { brandColors } from '../constants/colors'
import type { AnalysisSettings } from '../types/analysis'

export default function ConfigurationParameters() {
  const [form] = Form.useForm()
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const location = useLocation()
  const { globalSettings } = useAppSelector((state) => state.analysisSettings)
  
  // Get fileId from navigation state
  const fileId = (location.state as { fileId?: string })?.fileId

  useEffect(() => {
    if (globalSettings) {
      form.setFieldsValue(globalSettings)
    } else {
      form.setFieldsValue(defaultAnalysisSettings)
    }
  }, [globalSettings, form])

  const handleSubmit = async (values: AnalysisSettings) => {
    dispatch(setGlobalSettings(values))
    
    // Navigate to analysis with fileId if available
    if (fileId) {
      navigate(`/analysis/${fileId}`)
    } else {
      // Fallback to dashboard if no fileId
      navigate('/')
    }
  }

  const sectionHeaderStyle = {
    backgroundColor: brandColors.background.light,
    color: brandColors.text.secondary,
    padding: '16px 20px',
    fontSize: '15px',
    fontWeight: '600',
    borderBottom: `1px solid ${brandColors.border.light}`,
    margin: 0,
    letterSpacing: '0.025em',
  }

  const containerStyle = {
    backgroundColor: 'white',
    borderRadius: '12px',
    border: `1px solid ${brandColors.border.light}`,
    marginBottom: '32px',
    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  }

  return (
    <PageLayout>
      <div className="w-full max-w-4xl mx-auto">

        <PageHeader
          title="Configure Analysis Parameters"
          subtitle="Adjust signal thresholds, frequency filters, and synchronization settings below. These values will directly affect HFO detection and result accuracy. You can save your custom configuration or load a default one."
          variant="page"
        />

        <Form form={form} layout="vertical" onFinish={handleSubmit} className="space-y-8">
          {/* Threshold Settings */}
          <div style={containerStyle}>
            <h3 style={sectionHeaderStyle}>Threshold Settings</h3>
            <div className="p-8 grid grid-cols-2 gap-8">
              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Amplitude 1 *
                </label>
                <Form.Item name={['thresholdSettings', 'amplitude1']} className="mb-0">
                  <Select defaultValue={2} style={{ height: '44px', fontSize: '14px' }}>
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                  HFO amp {'>='} energy signal by: (times of std)
                </p>
              </div>

              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Amplitude 2 *
                </label>
                <Form.Item name={['thresholdSettings', 'amplitude2']} className="mb-0">
                  <Select defaultValue={5} style={{ height: '44px', fontSize: '14px' }}>
                    {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                  HFO amp {'>='} mean baseline signal by: (time of std)
                </p>
              </div>

              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Peaks 1 *
                </label>
                <Form.Item name={['thresholdSettings', 'peaks1']} className="mb-0">
                  <Select defaultValue={6} style={{ height: '44px', fontSize: '14px' }}>
                    {[3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                  HFO {'>='} Amplitude 1: (# peaks)
                </p>
              </div>

              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Peaks 2 *
                </label>
                <Form.Item name={['thresholdSettings', 'peaks2']} className="mb-0">
                  <Select defaultValue={5} style={{ height: '44px', fontSize: '14px' }}>
                    {[3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                  HFO {'>='} Amplitude 2: (# peaks)
                </p>
              </div>

              <div className="col-span-2">
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Duration (ms) *
                </label>
                <Form.Item name={['thresholdSettings', 'duration']} className="mb-0">
                  <Select defaultValue={10} style={{ height: '44px', fontSize: '14px' }}>
                    {[5, 10, 15, 20, 25, 30].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num} ms
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </div>
          </div>

          {/* Synchronization Settings */}
          <div style={containerStyle}>
            <h3 style={sectionHeaderStyle}>Synchronization Settings</h3>
            <div className="p-8 grid grid-cols-2 gap-8">
              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Temporal Sync (Inter-HFO) *
                </label>
                <Form.Item name={['synchronizationSettings', 'temporalSync']} className="mb-0">
                  <Select defaultValue={10} style={{ height: '44px', fontSize: '14px' }}>
                    {[5, 10, 15, 20, 25, 30].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num} ms
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                  Inter HFO interval in any channel {'<='} (T ms)
                </p>
              </div>

              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Spatial Sync (Cross-channels) *
                </label>
                <Form.Item name={['synchronizationSettings', 'spatialSync']} className="mb-0">
                  <Select defaultValue={10} style={{ height: '44px', fontSize: '14px' }}>
                    {[5, 10, 15, 20, 25, 30].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num} ms
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                  Inter HFO interval across channels {'<='} (T ms)
                </p>
              </div>
            </div>
          </div>

          {/* Montage Selection */}
          <div style={containerStyle}>
            <h3 style={sectionHeaderStyle}>Montage Selection</h3>
            <div className="p-8 grid grid-cols-3 gap-8">
              <Form.Item
                name={['montageSelection', 'bipolar']}
                valuePropName="checked"
                className="mb-0"
              >
                <Checkbox 
                  defaultChecked 
                  className="text-sm font-medium"
                  style={{ fontSize: '14px' }}
                >
                  <span style={{ color: brandColors.text.primary, fontWeight: '500' }}>
                    Bipolar
                  </span>
                </Checkbox>
              </Form.Item>
              <Form.Item
                name={['montageSelection', 'average']}
                valuePropName="checked"
                className="mb-0"
              >
                <Checkbox 
                  className="text-sm font-medium"
                  style={{ fontSize: '14px' }}
                >
                  <span style={{ color: brandColors.text.primary, fontWeight: '500' }}>
                    Average
                  </span>
                </Checkbox>
              </Form.Item>
              <Form.Item
                name={['montageSelection', 'referential']}
                valuePropName="checked"
                className="mb-0"
              >
                <Checkbox 
                  className="text-sm font-medium"
                  style={{ fontSize: '14px' }}
                >
                  <span style={{ color: brandColors.text.primary, fontWeight: '500' }}>
                    Referential
                  </span>
                </Checkbox>
              </Form.Item>
            </div>
          </div>

          {/* Frequency Filter Settings */}
          <div style={containerStyle}>
            <h3 style={sectionHeaderStyle}>Frequency Filter Settings</h3>
            <div className="p-6 grid grid-cols-2 gap-6">
              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  Low Cutoff Filter (Hz) *
                </label>
                <Form.Item name={['frequencyFilterSettings', 'lowCutoffFilter']} className="mb-0">
                  <Select defaultValue={50} style={{ height: '44px', fontSize: '14px' }}>
                    {[10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
                <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                  Should not exceed 1/3 of sampling rate
                </p>
              </div>

              <div>
                <label
                  className="block text-sm font-semibold mb-3"
                  style={{ color: brandColors.text.primary }}
                >
                  High Cutoff Filter (Hz) *
                </label>
                <Form.Item name={['frequencyFilterSettings', 'highCutoffFilter']} className="mb-0">
                  <Select defaultValue={500} style={{ height: '44px', fontSize: '14px' }}>
                    {[200, 300, 400, 500, 600, 700, 800, 900, 1000].map((num) => (
                      <Select.Option key={num} value={num}>
                        {num}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </div>
          </div>

          {/* Select Segment for Analysis */}
          <div style={containerStyle}>
            <h3 style={sectionHeaderStyle}>Select Segment for Analysis</h3>
            <div className="p-8 space-y-6">
              <div className="grid grid-cols-2 gap-8">
                <div>
                  <label
                    className="block text-sm font-semibold mb-3"
                    style={{ color: brandColors.text.primary }}
                  >
                    Low Cutoff Filter (Hz) *
                  </label>
                  <Form.Item
                    name={['segmentSelectionSettings', 'lowCutoffFilter']}
                    className="mb-0"
                  >
                    <Select defaultValue={50} style={{ height: '44px', fontSize: '14px' }}>
                      {[10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map((num) => (
                        <Select.Option key={num} value={num}>
                          {num}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                  <p className="text-xs mt-2 leading-relaxed" style={{ color: brandColors.text.muted }}>
                    Should not exceed 1/3 of sampling rate
                  </p>
                </div>

                <div>
                  <label
                    className="block text-sm font-semibold mb-3"
                    style={{ color: brandColors.text.primary }}
                  >
                    High Cutoff Filter (Hz) *
                  </label>
                  <Form.Item
                    name={['segmentSelectionSettings', 'highCutoffFilter']}
                    className="mb-0"
                  >
                    <Select defaultValue={500} style={{ height: '44px', fontSize: '14px' }}>
                      {[200, 300, 400, 500, 600, 700, 800, 900, 1000].map((num) => (
                        <Select.Option key={num} value={num}>
                          {num}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                </div>
              </div>

              <div className="space-y-5">
                <Form.Item
                  name={['segmentSelectionSettings', 'entireFile']}
                  valuePropName="checked"
                  className="mb-0"
                >
                  <Checkbox 
                    defaultChecked 
                    className="text-sm font-medium"
                    style={{ fontSize: '14px' }}
                  >
                    <span style={{ color: brandColors.text.primary, fontWeight: '500' }}>
                      Entire File
                    </span>
                  </Checkbox>
                </Form.Item>

                <div>
                  <Form.Item
                    name={['segmentSelectionSettings', 'startEndTimes']}
                    valuePropName="checked"
                    className="mb-3"
                  >
                    <Checkbox 
                      className="text-sm font-medium"
                      style={{ fontSize: '14px' }}
                    >
                      <span style={{ color: brandColors.text.primary, fontWeight: '500' }}>
                        Start / End times
                      </span>
                    </Checkbox>
                  </Form.Item>
                  <div className="grid grid-cols-4 gap-4 ml-6">
                    <div>
                      <label
                        className="block text-xs mb-1 font-medium"
                        style={{ color: brandColors.text.secondary }}
                      >
                        Start Date
                      </label>
                      <Form.Item name={['segmentSelectionSettings', 'startDate']} className="mb-0">
                        <Input style={{ height: '36px', fontSize: '14px' }} />
                      </Form.Item>
                    </div>
                    <div>
                      <label
                        className="block text-xs mb-1 font-medium"
                        style={{ color: brandColors.text.secondary }}
                      >
                        Start Time
                      </label>
                      <Form.Item name={['segmentSelectionSettings', 'startTime']} className="mb-0">
                        <Input style={{ height: '36px', fontSize: '14px' }} />
                      </Form.Item>
                    </div>
                    <div>
                      <label
                        className="block text-xs mb-1 font-medium"
                        style={{ color: brandColors.text.secondary }}
                      >
                        End Date
                      </label>
                      <Form.Item name={['segmentSelectionSettings', 'endDate']} className="mb-0">
                        <Input style={{ height: '36px', fontSize: '14px' }} />
                      </Form.Item>
                    </div>
                    <div>
                      <label
                        className="block text-xs mb-1 font-medium"
                        style={{ color: brandColors.text.secondary }}
                      >
                        End Time
                      </label>
                      <Form.Item name={['segmentSelectionSettings', 'endTime']} className="mb-0">
                        <Input style={{ height: '36px', fontSize: '14px' }} />
                      </Form.Item>
                    </div>
                  </div>
                </div>

                <div>
                  <Form.Item
                    name={['segmentSelectionSettings', 'startTimeDuration']}
                    valuePropName="checked"
                    className="mb-3"
                  >
                    <Checkbox 
                      className="text-sm font-medium"
                      style={{ fontSize: '14px' }}
                    >
                      <span style={{ color: brandColors.text.primary, fontWeight: '500' }}>
                        Start time / Duration
                      </span>
                    </Checkbox>
                  </Form.Item>
                  <div className="grid grid-cols-3 gap-4 ml-6">
                    <div>
                      <label
                        className="block text-xs mb-1 font-medium"
                        style={{ color: brandColors.text.secondary }}
                      >
                        Start Date
                      </label>
                      <Form.Item name={['segmentSelectionSettings', 'startDate']} className="mb-0">
                        <Input style={{ height: '32px' }} />
                      </Form.Item>
                    </div>
                    <div>
                      <label
                        className="block text-xs mb-1 font-medium"
                        style={{ color: brandColors.text.secondary }}
                      >
                        Start Time
                      </label>
                      <Form.Item name={['segmentSelectionSettings', 'startTime']} className="mb-0">
                        <Input style={{ height: '32px' }} />
                      </Form.Item>
                    </div>
                    <div>
                      <label
                        className="block text-xs mb-1 font-medium"
                        style={{ color: brandColors.text.secondary }}
                      >
                        Duration (sec): 90
                      </label>
                      <Form.Item
                        name={['segmentSelectionSettings', 'durationInSeconds']}
                        className="mb-0"
                      >
                        <Input style={{ height: '36px', fontSize: '14px' }} defaultValue="90" />
                      </Form.Item>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Continue Button */}
          <div className="flex justify-center pt-10 pb-8">
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              style={{
                backgroundColor: brandColors.actionButton,
                borderColor: brandColors.actionButton,
                height: '52px',
                paddingLeft: '40px',
                paddingRight: '40px',
                fontSize: '16px',
                fontWeight: '600',
                borderRadius: '12px',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                border: 'none',
                transition: 'all 0.2s ease-in-out',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = brandColors.secondary
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = '0 6px 12px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = brandColors.actionButton
                e.currentTarget.style.transform = 'translateY(0px)'
                e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
              }}
            >
              Continue
            </Button>
          </div>
        </Form>
      </div>
    </PageLayout>
  )
}
