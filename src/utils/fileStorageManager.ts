class FileStorageManager {
  private storage: Map<string, File>
  private refCount: number

  constructor() {
    this.storage = new Map()
    this.refCount = 0
  }

  addRef(): void {
    this.refCount++
  }

  removeRef(): void {
    this.refCount = Math.max(0, this.refCount - 1)

    if (this.refCount === 0) {
      this.storage.clear()
    }
  }

  set(id: string, file: File): void {
    this.storage.set(id, file)
  }

  get(id: string): File | undefined {
    return this.storage.get(id)
  }

  has(id: string): boolean {
    return this.storage.has(id)
  }

  delete(id: string): boolean {
    console.log(`[FileStorageManager] Deleting file with ID: ${id}`)
    return this.storage.delete(id)
  }

  clear(): void {
    console.log(`[FileStorageManager] Force clearing all files (count: ${this.storage.size})`)
    this.storage.clear()
  }

  getRefCount(): number {
    return this.refCount
  }

  getIds(): string[] {
    return Array.from(this.storage.keys())
  }

  size(): number {
    return this.storage.size
  }

  logState(context: string): void {
    console.log(
      `[FileStorageManager - ${context}] File count: ${
        this.storage.size
      }, IDs: ${this.getIds().join(', ')}`
    )
  }
}

export const fileStorageManager = new FileStorageManager()
